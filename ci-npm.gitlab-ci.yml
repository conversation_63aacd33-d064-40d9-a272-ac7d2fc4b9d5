include:
  - local: ci-base.gitlab-ci.yml
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-info@feat/testSonarCommunity
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-build@feat/testSonarCommunity
    inputs:
      build-number: $BUILD_NUMBER
      build-name: $BUILD_NAME
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/sonar-scan@feat/testSonarCommunity
  # - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-test@main
  # - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-code-analysis@main
  #   rules:
  #     - if: $SKIP_CODE_QUALITY != "true"
  # - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-code-coverage-report@main
  #   rules:
  #     - if: $SKIP_CODE_QUALITY != "true"

    