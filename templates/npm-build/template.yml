spec:
  inputs:
    build-name:
    build-number:
    build-stage:
      default: build
    node-image-name:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/nodejs:22-latest
    jfrog-project:
      default: bint
---
include:
  - local: "/templates/.jfrog.gitlab-ci.yml"

npm-build:
  variables:
    KUBERNETES_MEMORY_LIMIT: 4Gi
    KUBERNETES_EPHEMERAL_STORAGE_LIMIT: 8Gi
  stage: $[[ inputs.build-stage ]]
  extends: [.jf_npm]
  rules:
    - if: $CI_COMMIT_BRANCH || $CI_PIPELINE_SOURCE == "merge_request_event"
  image: $[[ inputs.node-image-name ]]
  script:
    - |
      if [ ! -d "node_modules" ]; then
        jf npm ci --build-name="${BUILD_NAME}" --build-number="${BUILD_NUMBER}" ;
      fi
    - jf npm run build --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]]


npm-build-publish:
  stage: $[[ inputs.build-stage ]]
  image: $[[ inputs.node-image-name ]]
  extends: [.jf_npm]
  variables:
    KUBERNETES_MEMORY_LIMIT: 4Gi
    KUBERNETES_EPHEMERAL_STORAGE_LIMIT: 5Gi
  rules:
    - if: $CI_COMMIT_TAG
  script:
    - |
      if [ ! -d "node_modules" ]; then
        jf npm ci --build-name="${BUILD_NAME}" --build-number="${BUILD_NUMBER}" ;
      fi
    - jf npm run build --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]]
    - jf npm publish --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]] --project=$[[ inputs.jfrog-project ]]

