spec:
  inputs:
    test-stage:
      default: post-test
    java-image:
      default: bint-docker-dfe.docker-artifactory.dach041.dachser.com/ci/java-17-node-build-image:latest
    sonar-image:
      # default: bint-docker-dfe.docker-artifactory.dach041.dachser.com/ci/sonar-scanner-npm:1.0.0
      default: docker-all.dach041.dachser.com/amaral79/sonar-scanner-npm:2.0.0
    sonar-project-update-image:
      default: docker-all.dach041.dachser.com/ubi8/ubi-minimal:8.2
    sonar-project-name:
      default: ""
    sonar-project-version:
      default: ""

---
include:
  - local: "/templates/.jfrog.gitlab-ci.yml"
  - local: "/templates/.certificates.gitlab-ci.yml"

java-sonar-scan:
  stage: $[[ inputs.test-stage ]]
  extends: [.jf_java]
  rules:
    - if: $SONAR_SCAN_DISABLED == 'true' || $SONAR_SCAN_DISABLED == '1'
      when: never
    # SonarQube CE: Temporarily allowing all branches for testing
    - if: $CI_COMMIT_BRANCH
      exists:
        - pom.xml
  image: $[[ inputs.java-image ]]
  variables:
    # SONAR_BRANCH_OPTIONS: "-Dsonar.branch.name=$CI_COMMIT_REF_NAME" # CE doesn't support branches
    SONAR_GOAL: "org.sonarsource.scanner.maven:sonar-maven-plugin:3.9.1.2184:sonar"
    SONAR_PROJECT_KEY: "$MAVEN_GROUP_ID:$MAVEN_ARTIFACT_ID"
  script:
    - |
      jf mvn $SONAR_GOAL $MAVEN_ARGS \
      -Dsonar.projectKey="$SONAR_PROJECT_KEY" -Dsonar.projectName="$MAVEN_GROUP_ID - $MAVEN_ARTIFACT_ID" \
      -Dsonar.projectVersion=$MAVEN_VERSION -Dsonar.host.url=$SONAR_HOST_URL \
      -Dsonar.scm.provider=git -Dsonar.links.scm=$CI_REPOSITORY_URL -Dsonar.token=$SONAR_TOKEN

# SonarQube CE: NPM scanning simplified for Community Edition
npm-sonar-scan:
  stage: $[[ inputs.test-stage ]]
  extends: [.jf_java, .java-truststore]
  rules:
    - if: $SONAR_SCAN_DISABLED == 'true' || $SONAR_SCAN_DISABLED == '1'
      when: never
    # SonarQube CE: Temporarily allowing all branches for testing
    - if: $CI_COMMIT_BRANCH
      exists:
        - package.json
  image:
    name: $[[ inputs.sonar-image ]]
    entrypoint: [ "" ]
  variables:
    GIT_DEPTH: "0"
    SONAR_PROJECT_NAME: $NPM_NAME
    SONAR_PROJECT_VERSION: $NPM_VERSION
    SOURCE_ENCODING: "UTF-8"
    JAVA_TRUSTSTORE: "/usr/lib/jvm/java-17-openjdk/lib/security/cacerts"
    NPM_REGISTRY: http://artifactory.dach041.dachser.com/artifactory/api/npm/npm-official/
    DFE_REGISTRY: http://artifactory.dach041.dachser.com/artifactory/api/npm/bint-npm-dfe
  script:
    - !reference [ .java-truststore, script ]
    - |
      touch sonar-project.properties
      SONAR_PROJECT_KEY=$SONAR_PROJECT_NAME
      SONAR_PROJECT_KEY="${SONAR_PROJECT_KEY////-}"
      SONAR_PROJECT_KEY="${SONAR_PROJECT_KEY/@/at-}"
      SONAR_PROJECT_KEY=$(echo "$SONAR_PROJECT_KEY" | sed -r 's/([A-Z])/-\L\1/g' | sed 's/^-//')

      echo "sonar.projectKey=$SONAR_PROJECT_KEY" >> sonar-project.properties
      echo "sonar.projectName=$SONAR_PROJECT_NAME" >> sonar-project.properties
      echo "sonar.projectVersion=$SONAR_PROJECT_VERSION" >> sonar-project.properties
      echo "sonar.host.url=$SONAR_HOST_URL" >> sonar-project.properties
      echo "sonar.sourceEncoding=$SOURCE_ENCODING" >> sonar-project.properties
      echo "sonar.exclusions=src/dev/**/*,src/types/**/*,src/**/generated*,src/mocks/**/*,**/*.spec.ts" >> sonar-project.properties

      echo "sonar.sources=src" >> sonar-project.properties
      echo "sonar.javascript.lcov.reportPaths=coverage/lcov.info" >> sonar-project.properties

      echo "sonar.scm.provider=git" >> sonar-project.properties

      # SonarQube CE: Remove branch/PR logic - CE doesn't support it
      # if [ -n "$CI_MERGE_REQUEST_ID" ]
      # then
      #   echo "sonar.pullrequest.branch=$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME" >> sonar-project.properties
      #   echo "sonar.pullrequest.key=$CI_MERGE_REQUEST_ID" >> sonar-project.properties
      #   echo "sonar.pullrequest.base=$CI_MERGE_REQUEST_TARGET_BRANCH_NAME" >> sonar-project.properties
      #   echo "sonar.qualitygate.wait=true"  >> sonar-project.properties
      # else
      #   echo "sonar.branch.name=$CI_COMMIT_BRANCH" >> sonar-project.properties
      # fi

      echo "sonar.links.scm=$CI_REPOSITORY_URL" >> sonar-project.properties
      echo "sonar.token=$SONAR_TOKEN" >> sonar-project.properties
    - npm config set -g registry $NPM_REGISTRY
    - npm config set -g @dfe:registry $DFE_REGISTRY
    - npm install
    - sonar-scanner -Dsonar.qualitygate.wait=true


# SonarQube CE: ALM integrations not available in Community Edition
# update-sonar-node-project:
#   stage: $[[ inputs.test-stage ]]
#   variables:
#     SONAR_PROJECT_KEY: "$[[ inputs.sonar-project-name ]]:$[[ inputs.sonar-project-version ]]"
#     PARAMETER: "almSetting=Gitlab&project=$SONAR_PROJECT_KEY&repository=$CI_PROJECT_ID&slug=$CI_PROJECT_NAMESPACE"
#   rules:
#     - if: $SONAR_SCAN_DISABLED == 'true' || $SONAR_SCAN_DISABLED == '1'
#       when: never
#     - if: $CI_COMMIT_BRANCH || $CI_PIPELINE_SOURCE == "merge_request_event"
#       exists:
#         - package.json
#   image:  $[[ inputs.sonar-project-update-image ]]
#   script:
#     - |
#       curl --insecure \
#       -u $SONAR_TOKEN: \
#       --request POST \
#       ${SONAR_HOST_URL}api/alm_settings/set_gitlab_binding?$PARAMETER

# update-sonar-java-project:
#   stage: $[[ inputs.test-stage ]]
#   variables:
#     SONAR_PROJECT_KEY: "$MAVEN_GROUP_ID:$MAVEN_ARTIFACT_ID"
#     PARAMETER: "almSetting=Gitlab&project=$SONAR_PROJECT_KEY&repository=$CI_PROJECT_ID&slug=$CI_PROJECT_NAMESPACE"
#   rules:
#     - if: $SONAR_SCAN_DISABLED == 'true' || $SONAR_SCAN_DISABLED == '1'
#       when: never
#     - if: $CI_COMMIT_BRANCH || $CI_PIPELINE_SOURCE == "merge_request_event"
#       exists:
#         - pom.xml
#   image:  $[[ inputs.sonar-project-update-image ]]
#   script:
#     - |
#       curl --insecure \
#       -u $SONAR_TOKEN: \
#       --request POST \
#       ${SONAR_HOST_URL}api/alm_settings/set_gitlab_binding?$PARAMETER

